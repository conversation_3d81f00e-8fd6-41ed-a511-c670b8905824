# Custom Data: ToolUsagePatterns

### development_environment

```json
{
  "python_version": "Python 3.10.11",
  "git_version": "2.49.0.windows.1",
  "venv_path": "c:/Users/<USER>/Projects/nbadb/Database/venv",
  "project_root": "c:/Users/<USER>/Projects/nbadb/Database",
  "operating_system": "Windows 11"
}
```

---
### etl_commands

```json
{
  "test_players_fix": "python etl/run_etl.py --sources kaggle_eoin_moore --tables eoin_players",
  "general_etl_run": "python etl/run_etl.py --sources [source_name] --tables [table_name]",
  "log_monitoring": "Check logs/etl.log and timestamped logs for processing status"
}
```

---
### mcp_configuration

```json
{
  "config_file_path": "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\saoudrizwan.claude-dev\\settings\\cline_mcp_settings.json",
  "postgresql_server_config": {
    "type": "stdio",
    "command": "node",
    "args": [
      "C:\\\\Program Files\\\\nodejs\\\\node_modules\\\\npm\\\\bin\\\\npx-cli.js",
      "-y",
      "@modelcontextprotocol/server-postgres",
      "postgresql://user:password@localhost:5432/nba_data_db"
    ],
    "env": {},
    "disabled": false,
    "autoApprove": [],
    "alwaysAllow": []
  }
}
```
