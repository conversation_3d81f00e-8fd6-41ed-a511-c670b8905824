# Custom Data: ConfigurationDetails

### column_renaming_sections

```json
{
  "PlayerStatistics": "[ColumnRenames_PlayerStatistics]",
  "TeamStatistics": "[ColumnRenames_TeamStatistics]",
  "Games": "[ColumnRenames_Games]",
  "LeagueSchedule": "[ColumnRenames_LeagueSchedule]",
  "Players": "[ColumnRenames_Players]",
  "example_mapping": "height = height_inches"
}
```

---
### file_structure

```json
{
  "etl_directory": [
    "run_etl.py",
    "etl_orchestrator.py",
    "extractors.py",
    "transformers.py",
    "loaders.py",
    "schema_utils.py"
  ],
  "database_directory": [
    "db_manager.py",
    "schema_ddl/"
  ],
  "data_directory": "data/ with subfolders for datasets",
  "logs_directory": "logs/ for ETL log files",
  "docs_directory": "docs/ with data_dictionary.md"
}
```
