# Custom Data: MCPServers

### available_servers

```json
{
  "default_servers": [
    "context7-mcp",
    "perplexity-mcp",
    "sequentialthinking"
  ],
  "development_tools": [
    "@modelcontextprotocol/inspector",
    "@modelcontextprotocol/create-python-server",
    "@modelcontextprotocol/create-typescript-server"
  ],
  "discovery_resource": "planning/nbadb/mcplist.md contains 50 potentially useful MCP servers"
}
```

---
### postgresql_server

```json
{
  "server": "@modelcontextprotocol/server-postgres",
  "purpose": "Read-only access to nba_data_db PostgreSQL database",
  "configuration_file": "cline_mcp_settings.json",
  "connection_string": "postgresql://user:password@localhost:5432/nba_data_db",
  "status": "Configured and functional"
}
```
