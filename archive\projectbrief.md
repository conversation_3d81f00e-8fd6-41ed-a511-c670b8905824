# Project Brief

## Core Requirements & Goals

*   To create a local, queryable PostgreSQL database populated with a highly granular set of historical NBA, BAA, NBL, and ABA data (1946-2025).
*   Utilize exclusively free and open-source software and tools.
*   Provide a comprehensive, locally accessible dataset for personal data analysis, historical research, and basketball-related projects.

## Scope
*   Data Collection: Ingest data from Kaggle datasets (<PERSON><PERSON><PERSON>, <PERSON>), NBA.com API, and Basketball-Reference.com (via library). Spotrac.com and custom scraping components are planned but not yet explicitly implemented in the main orchestrator.
*   Data Coverage: NBA, BAA, NBL, ABA leagues, from 1946 to 2025. Data includes player information, team information, game statistics, season statistics, awards, draft details, transactions, and contract information.
*   ETL Pipeline: Develop a Python-based ETL (Extract, Transform, Load) pipeline to manage data ingestion, cleaning, transformation, conflict resolution, and loading into a PostgreSQL database. (Confirmed in progress with `etl/` directory structure and scripts).
*   Database: Design and implement a PostgreSQL database schema to store the collected data in a structured and queryable format.
*   Documentation: Create foundational project documentation, including a data dictionary, README, ETL process overview, and notes on historical nuances.
*   Querying: Enable users to query the populated database using standard SQL clients.
