# Custom Data: TechnicalStack

### core_libraries

```json
{
  "data_processing": [
    "pandas",
    "numpy"
  ],
  "database": [
    "psycopg2-binary"
  ],
  "web_scraping": [
    "requests",
    "beautifulsoup4",
    "Scrapy"
  ],
  "apis": [
    "nba-api",
    "basketball-reference-web-scraper"
  ],
  "configuration": [
    "python-dotenv"
  ],
  "additional": [
    "sqlalchemy",
    "fuzzywuzzy"
  ]
}
```

---
### database

```json
{
  "system": "PostgreSQL",
  "version": "17",
  "database_name": "nba_data_db",
  "setup_status": "Complete",
  "connection": "Local instance, credentials via .env"
}
```

---
### programming_language

```json
{
  "language": "Python 3.9+",
  "version_confirmed": "Python 3.10.11",
  "environment": "Virtual environment (venv) at c:/Users/<USER>/Projects/nbadb/Database/venv",
  "status": "Active and confirmed"
}
```
