# Custom Data: ComponentInteractions

### data_flow_patterns

```json
{
  "extraction": {
    "sources": [
      "Kaggle CSVs",
      "NBA API",
      "Basketball-Reference"
    ],
    "classes": [
      "KaggleCSVExtractor",
      "NBAAPIExtractor",
      "WyattKaggleCSVExtractor"
    ],
    "output": "pandas DataFrames"
  },
  "transformation": {
    "input": "DataFrames from extractors",
    "classes": [
      "KaggleTransformer",
      "NBAAPITransformer",
      "WyattFranchiseTeamTransformer"
    ],
    "special_logic": "_transform_eoin_moore_data method in etl_orchestrator.py",
    "output": "processed DataFrames"
  },
  "loading": {
    "classes": [
      "PostgreSQLLoader"
    ],
    "database_interface": "DatabaseManager",
    "key_method": "load_df_to_table"
  }
}
```

---
### database_interaction_patterns

```json
{
  "connection_management": "database/db_manager.py handles all PostgreSQL interactions",
  "ddl_execution": "DDL scripts from database/schema_ddl/ executed in numerical order",
  "data_loading": "pandas DataFrames loaded via load_df_to_table method",
  "idempotency_strategies": [
    "ON CONFLICT clauses",
    "table truncation for full reloads"
  ],
  "credential_management": "credentials from .env file"
}
```

---
### etl_orchestrator_flow

```json
{
  "entry_point": "etl/run_etl.py",
  "orchestrator": "etl/etl_orchestrator.py",
  "config_handling": {
    "config_ini": "handled by etl_orchestrator.py",
    "env_file": "handled by db_manager.py"
  },
  "logging": "logs/etl_TIMESTAMP.log",
  "schema_initialization": "via DatabaseManager and DDL scripts"
}
```
