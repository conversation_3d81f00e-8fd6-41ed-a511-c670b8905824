# Custom Data: TechnicalConstraints

### rate_limiting

```json
{
  "constraint": "API Rate Limiting and Politeness",
  "description": "Strict adherence to API rate limits and website robots.txt/Terms of Service required",
  "implementation": "Configurable delays between requests mandatory for all external data sources"
}
```

---
### software_requirements

```json
{
  "constraint": "Free and Open Source",
  "description": "All software and tools must be free and open-source",
  "compliance": "PostgreSQL, Python, all libraries meet this requirement"
}
```
