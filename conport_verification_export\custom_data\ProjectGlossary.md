# Custom Data: ProjectGlossary

### Column_Renaming_Pattern

```json
ETL standardization approach where CSV column names are converted to lowercase, then mapped to database schema column names (often camelCase) via config.ini [ColumnRenames_TableName] sections. Critical pattern for handling diverse data source formats.
```

---
### Configuration_Driven_Architecture

```json
System design pattern where key parameters (file paths, API delays, column mappings, database credentials) are managed through external configuration files (config.ini, .env) rather than hardcoded in source code. Enables dynamic behavior and environment-specific settings.
```

---
### ETL_Pipeline

```json
Extract, Transform, Load pipeline implemented in Python with modular architecture. Core components: etl_orchestrator.py (main controller), extractors.py (data extraction), transformers.py (data transformation), loaders.py (data loading), run_etl.py (CLI entry point)
```
