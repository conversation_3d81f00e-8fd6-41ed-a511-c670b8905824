# Custom Data: config_fix

### PlayerStatistics_column_mapping

```json
{
  "firstname": "firstName",
  "lastname": "lastName",
  "personid": "personId",
  "gameid": "gameId",
  "gamedate": "gameDate",
  "playerteamcity": "playerteamCity",
  "playerteamname": "playerteamName",
  "opponentteamcity": "opponentteamCity",
  "opponentteamname": "opponentteamName",
  "gametype": "gameType",
  "gamelabel": "gameLabel",
  "gamesublabel": "gameSubLabel",
  "seriesgamenumber": "seriesGameNumber",
  "win": "win",
  "home": "home",
  "numminutes": "numMinutes",
  "points": "points",
  "assists": "assists",
  "blocks": "blocks",
  "steals": "steals",
  "fieldgoalsattempted": "fieldGoalsAttempted",
  "fieldgoalsmade": "fieldGoalsMade",
  "fieldgoalspercentage": "fieldGoalsPercentage",
  "threepointersattempted": "threePointersAttempted",
  "threepointersmade": "threePointersMade",
  "threepointerspercentage": "threePointersPercentage",
  "freethrowsattempted": "freeThrowsAttempted",
  "freethrowsmade": "freeThrowsMade",
  "freethrowspercentage": "freeThrowsPercentage",
  "reboundsdefensive": "reboundsDefensive",
  "reboundsoffensive": "reboundsOffensive",
  "reboundstotal": "reboundsTotal",
  "foulspersonal": "foulsPersonal",
  "turnovers": "turnovers",
  "plusminuspoints": "plusMinusPoints"
}
```
