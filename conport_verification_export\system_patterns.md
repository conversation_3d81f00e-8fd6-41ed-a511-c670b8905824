# System Patterns

## ConPort Integration Pattern
Use ConPort to maintain project knowledge including ETL pipeline decisions, debugging progress, and configuration patterns. Semantic search helps find related issues quickly.

## Configuration Management Pattern
External configuration files (config.ini, .env) manage key parameters like file paths, API delays, database credentials, and column mappings. Enables environment-specific settings and dynamic behavior without code changes.

## Configuration-Driven Column Mapping Pattern
ETL standardizes CSV columns to lowercase, then config.ini [ColumnRenames_TableName] sections map to database camelCase columns. Pattern: CSV -> lowercase standardization -> config.ini mapping -> database schema. Example: height = height_inches in [ColumnRenames_Players]. Critical for handling diverse data source formats.

## Data Access Object Pattern
database/db_manager.py encapsulates all database interaction logic, providing clean interface for DDL execution, query execution, and DataFrame loading. Centralizes database operations and connection management.

## Database Interaction and Idempotency Pattern
db_manager.py centralizes all PostgreSQL operations with idempotency strategies: ON CONFLICT clauses for upserts, table truncation for full reloads. DDL scripts executed in numerical order from schema_ddl/. load_df_to_table method handles pandas DataFrame insertion with chunking for large datasets.

## ETL Component Interaction Flow
Detailed interaction pattern: run_etl.py (entry) -> etl_orchestrator.py (coordinator) -> extractors.py (data fetch) -> transformers.py (data processing) -> loaders.py (database insert) -> db_manager.py (PostgreSQL interface). Each component passes pandas DataFrames and uses specific class instances like WyattKaggleCSVExtractor, WyattFranchiseTeamTransformer, PostgreSQLLoader.

## Error Resolution and Debugging Pattern
Iterative debugging approach: 1) Identify specific error (NumericValueOutOfRange, UndefinedColumn), 2) Locate root cause (transform condition, column mapping), 3) Apply targeted fix (code change, config update), 4) Test with specific command, 5) Monitor logs for verification. Transform identifier matching ensures correct logic triggering.

## Layered ETL Architecture
Multi-layered ETL pipeline with distinct Extraction Layer (extractors.py), Transformation Layer (transformers.py, etl_orchestrator.py), Loading Layer (loaders.py, db_manager.py), Data Storage Layer (PostgreSQL), and Orchestration Layer (etl_orchestrator.py, run_etl.py). Promotes separation of concerns and maintainability.

## Logging and Monitoring Pattern
Comprehensive logging using Python logging module: logs stored in logs/ directory with timestamped files (etl_TIMESTAMP.log). Debug logging enabled for troubleshooting. Log monitoring via logs/etl.log for real-time status. Critical for tracking ETL operations, errors, and data flow.

## MCP Server Integration Pattern
MCP servers configured via cline_mcp_settings.json with specific paths and arguments. PostgreSQL MCP server uses full npx path with node command for Windows compatibility. Configuration includes stdio type, command args, and connection strings. Provides read-only database access for development tools.

## Modular Component Design
System broken down into smaller, independent, interchangeable modules/classes (DatabaseManager, ETLOrchestrator, various Extractor/Loader classes). Each data source and processing step encapsulated in own module.

## Orchestrator Controller Pattern
etl_orchestrator.py acts as central controller for ETL workflow, coordinating extractors, transformers, and loaders. Manages execution flow and provides single point of control for complex multi-step processes.

## Strategy Pattern for Data Sources
Different extractor and transformer classes (KaggleCSVExtractor, NBAAPIExtractor, WyattFranchiseTeamTransformer) act as interchangeable strategies for handling different data sources and transformation requirements.
