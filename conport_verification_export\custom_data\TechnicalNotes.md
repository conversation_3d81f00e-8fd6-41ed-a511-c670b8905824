# Custom Data: TechnicalNotes

### data_processing_volumes

```json
{
  "PlayerStatistics": {
    "rows_processed": 1627146,
    "status": "Successfully processed",
    "config_requirement": "[ColumnRenames_PlayerStatistics] section"
  },
  "TeamStatistics": {
    "status": "Successfully processed after config correction"
  },
  "Games": {
    "target_table": "game_logs",
    "status": "Successfully processed after casing correction"
  },
  "LeagueSchedule": {
    "target_table": "eoin_league_schedule",
    "status": "Successfully processed"
  }
}
```

---
### error_patterns

```json
{
  "NumericValueOutOfRange": {
    "error_type": "Players.csv processing error",
    "target_table": "eoin_players",
    "resolution": "Correct transformation condition in _transform_eoin_moore_data"
  },
  "UndefinedColumn": {
    "error_example": "column \"height\" of relation \"eoin_players\" does not exist",
    "resolution": "Correct keys in [ColumnRenames_Players] config.ini section"
  },
  "transform_identifier_issue": {
    "problem": "elif file_name == 'Players.csv':",
    "solution": "elif file_name == 'Players':"
  }
}
```
